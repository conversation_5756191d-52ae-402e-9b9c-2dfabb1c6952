import psutil
from typing import Optional, Dict, Any
from dataclasses import dataclass


@dataclass
class BatteryInfo:
    """Data class to hold battery information."""
    percent: float
    power_plugged: bool
    time_left_seconds: Optional[int]
    time_left_formatted: Optional[str]
    status: str


class BatteryMonitor:
    """
    A class to monitor and retrieve system battery information.
    """
    
    def __init__(self):
        """Initialize the BatteryMonitor."""
        pass
    
    def get_battery_level(self) -> Optional[BatteryInfo]:
        """
        Check the current system battery level and status.
        
        Returns:
            Optional[BatteryInfo]: BatteryInfo object containing battery information,
            or None if no battery is detected.
        """
        try:
            battery = psutil.sensors_battery()
            
            if battery is None:
                return None
            
            # Calculate time left in a more readable format
            time_left_formatted = None
            time_left_seconds = None
            
            if (battery.secsleft != psutil.POWER_TIME_UNLIMITED and 
                battery.secsleft != psutil.POWER_TIME_UNKNOWN):
                time_left_seconds = battery.secsleft
                hours = battery.secsleft // 3600
                minutes = (battery.secsleft % 3600) // 60
                time_left_formatted = f"{hours}h {minutes}m"
            
            # Determine battery status
            status = self._determine_battery_status(battery.percent, battery.power_plugged)
            
            return BatteryInfo(
                percent=battery.percent,
                power_plugged=battery.power_plugged,
                time_left_seconds=time_left_seconds,
                time_left_formatted=time_left_formatted,
                status=status
            )
            
        except Exception as e:
            print(f"Error retrieving battery information: {e}")
            return None
    
    def _determine_battery_status(self, percent: float, power_plugged: bool) -> str:
        """
        Determine the battery status based on percentage and power state.
        
        Args:
            percent: Battery percentage (0-100)
            power_plugged: Whether the power adapter is connected
            
        Returns:
            str: Battery status description
        """
        if power_plugged:
            if percent == 100:
                return "Fully charged"
            else:
                return "Charging"
        else:
            if percent <= 10:
                return "Critical"
            elif percent <= 20:
                return "Low"
            else:
                return "Discharging"
    
    def is_battery_available(self) -> bool:
        """
        Check if a battery is available on the system.
        
        Returns:
            bool: True if battery is detected, False otherwise
        """
        try:
            battery = psutil.sensors_battery()
            return battery is not None
        except Exception:
            return False
    
    def is_battery_critical(self, threshold: float = 10.0) -> bool:
        """
        Check if battery level is critical.
        
        Args:
            threshold: Battery percentage threshold for critical level (default: 10%)
            
        Returns:
            bool: True if battery is critical, False otherwise
        """
        battery_info = self.get_battery_level()
        if battery_info is None:
            return False
        return battery_info.percent <= threshold and not battery_info.power_plugged
    
    def print_battery_info(self) -> None:
        """
        Print formatted battery information to the console.
        """
        if not self.is_battery_available():
            print("No battery detected on this system.")
            return
        
        battery_info = self.get_battery_level()
        
        if battery_info is None:
            print("Unable to retrieve battery information.")
            return
        
        print("=== Battery Information ===")
        print(f"Battery Level: {battery_info.percent}%")
        print(f"Status: {battery_info.status}")
        print(f"Power Adapter: {'Connected' if battery_info.power_plugged else 'Disconnected'}")
        
        if battery_info.time_left_formatted:
            print(f"Estimated Time Left: {battery_info.time_left_formatted}")
        elif battery_info.power_plugged:
            print("Estimated Time Left: N/A (Charging)")
        else:
            print("Estimated Time Left: Unknown")
    
    def get_battery_percentage(self) -> Optional[float]:
        """
        Get just the battery percentage.
        
        Returns:
            Optional[float]: Battery percentage or None if no battery
        """
        battery_info = self.get_battery_level()
        return battery_info.percent if battery_info else None
    
    def is_charging(self) -> Optional[bool]:
        """
        Check if the battery is currently charging.
        
        Returns:
            Optional[bool]: True if charging, False if not, None if no battery
        """
        battery_info = self.get_battery_level()
        if battery_info is None:
            return None
        return battery_info.power_plugged and battery_info.percent < 100

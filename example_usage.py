"""
Example usage of the BatteryMonitor class.
"""

from src.battery_monitor import BatteryMonitor


def main():
    """Demonstrate various BatteryMonitor methods."""
    
    # Create a battery monitor instance
    battery_monitor = BatteryMonitor()
    
    # Check if battery is available
    if not battery_monitor.is_battery_available():
        print("No battery detected on this system.")
        return
    
    print("Battery Monitor Example Usage")
    print("=" * 40)
    
    # Get detailed battery information
    battery_info = battery_monitor.get_battery_level()
    if battery_info:
        print(f"Raw battery data:")
        print(f"  Percent: {battery_info.percent}%")
        print(f"  Power plugged: {battery_info.power_plugged}")
        print(f"  Time left (seconds): {battery_info.time_left_seconds}")
        print(f"  Time left (formatted): {battery_info.time_left_formatted}")
        print(f"  Status: {battery_info.status}")
        print()
    
    # Use convenience methods
    print("Convenience methods:")
    print(f"  Battery percentage: {battery_monitor.get_battery_percentage()}%")
    print(f"  Is charging: {battery_monitor.is_charging()}")
    print(f"  Is critical (< 10%): {battery_monitor.is_battery_critical()}")
    print(f"  Is critical (< 25%): {battery_monitor.is_battery_critical(25.0)}")
    print()
    
    # Print formatted output
    print("Formatted output:")
    battery_monitor.print_battery_info()


if __name__ == "__main__":
    main()

import psutil
from typing import Optional, Dict, Any


def get_battery_level() -> Optional[Dict[str, Any]]:
    """
    Check the current system battery level and status.

    Returns:
        Optional[Dict[str, Any]]: Dictionary containing battery information:
            - percent: Battery percentage (0-100)
            - power_plugged: Whether the power adapter is plugged in
            - time_left: Estimated time left in seconds (None if plugged in or unknown)
            - status: Battery status string
        Returns None if no battery is detected.
    """
    try:
        battery = psutil.sensors_battery()

        if battery is None:
            print("No battery detected on this system.")
            return None

        # Calculate time left in a more readable format
        time_left_formatted = None
        if battery.secsleft != psutil.POWER_TIME_UNLIMITED and battery.secsleft != psutil.POWER_TIME_UNKNOWN:
            hours = battery.secsleft // 3600
            minutes = (battery.secsleft % 3600) // 60
            time_left_formatted = f"{hours}h {minutes}m"

        # Determine battery status
        if battery.power_plugged:
            if battery.percent == 100:
                status = "Fully charged"
            else:
                status = "Charging"
        else:
            if battery.percent <= 10:
                status = "Critical"
            elif battery.percent <= 20:
                status = "Low"
            else:
                status = "Discharging"

        battery_info = {
            "percent": battery.percent,
            "power_plugged": battery.power_plugged,
            "time_left_seconds": battery.secsleft if battery.secsleft not in [psutil.POWER_TIME_UNLIMITED, psutil.POWER_TIME_UNKNOWN] else None,
            "time_left_formatted": time_left_formatted,
            "status": status
        }

        return battery_info

    except Exception as e:
        print(f"Error retrieving battery information: {e}")
        return None


def print_battery_info() -> None:
    """
    Print formatted battery information to the console.
    """
    battery_info = get_battery_level()

    if battery_info is None:
        return

    print("=== Battery Information ===")
    print(f"Battery Level: {battery_info['percent']}%")
    print(f"Status: {battery_info['status']}")
    print(f"Power Adapter: {'Connected' if battery_info['power_plugged'] else 'Disconnected'}")

    if battery_info['time_left_formatted']:
        print(f"Estimated Time Left: {battery_info['time_left_formatted']}")
    elif battery_info['power_plugged']:
        print("Estimated Time Left: N/A (Charging)")
    else:
        print("Estimated Time Left: Unknown")


def main():
    """
    Main function to demonstrate battery level checking.
    """
    print_battery_info()


if __name__ == "__main__":
    main()